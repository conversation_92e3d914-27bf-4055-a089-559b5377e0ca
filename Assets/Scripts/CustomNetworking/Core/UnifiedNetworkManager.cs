using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;
using CustomNetworking.Core.RPC;
using CustomNetworking.Synchronization;
using CustomNetworking.ErrorHandling;

namespace CustomNetworking.Core
{
    /// <summary>
    /// 统一网络管理器 - 整合所有网络功能的核心管理器
    /// 使用自定义网络框架，支持WebSocket传输、RPC、状态同步等
    /// </summary>
    public class UnifiedNetworkManager : MonoBehaviour, INetworkManager
    {
        [Header("网络配置")]
        [SerializeField] private NetworkRunner.GameMode gameMode = NetworkRunner.GameMode.Shared;
        [SerializeField] private string serverAddress = "localhost";
        [SerializeField] private int serverPort = 7777;
        [SerializeField] private int maxPlayers = 16;

        [Header("传输层配置")]
        [SerializeField] private NetworkTransportType transportType = NetworkTransportType.WebSocket;
        [SerializeField] private bool enableCompression = true;
        [SerializeField] private bool enableEncryption = false;

        [Header("RPC配置")]
        [SerializeField] private bool enableRpcOptimization = true;
        [SerializeField] private int maxRpcPerFrame = 50;
        [SerializeField] private bool enableRpcStatistics = true;

        [Header("同步配置")]
        [SerializeField] private bool enableStateSynchronization = true;
        [SerializeField] private float syncRate = 20f;
        [SerializeField] private bool enableDeltaCompression = true;

        [Header("调试")]
        [SerializeField] private bool enableDebugLogs = true;
        [SerializeField] private bool showNetworkStats = false;

        #region 私有字段

        private NetworkRunner _runner;
        private INetworkTransport _transport;
        private UnifiedNetworkConnection _connection;
        private OptimizedStateSynchronizer _stateSynchronizer;
        private NetworkErrorHandler _errorHandler;

        // 管理器状态
        private NetworkManagerState _currentState = NetworkManagerState.Disconnected;
        private bool _isInitialized = false;

        // 统计信息
        private NetworkManagerStatistics _statistics = new NetworkManagerStatistics();

        // 事件
        public event Action<NetworkManagerState> OnStateChanged;
        public event Action<PlayerRef> OnPlayerJoined;
        public event Action<PlayerRef> OnPlayerLeft;
        public event Action<string> OnConnectionError;

        #endregion

        #region 单例

        private static UnifiedNetworkManager _instance;
        public static UnifiedNetworkManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindObjectOfType<UnifiedNetworkManager>();
                    if (_instance == null)
                    {
                        var go = new GameObject("UnifiedNetworkManager");
                        _instance = go.AddComponent<UnifiedNetworkManager>();
                        DontDestroyOnLoad(go);
                    }
                }
                return _instance;
            }
        }

        #endregion

        #region Unity生命周期

        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeManager();
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            if (_isInitialized)
            {
                SetupComponents();
            }
        }

        private void Update()
        {
            if (_isInitialized && _runner != null)
            {
                _runner.Update();
                UpdateStatistics();

                if (showNetworkStats)
                {
                    UpdateDebugDisplay();
                }
            }
        }

        private void OnDestroy()
        {
            if (_instance == this)
            {
                CleanupManager();
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 启动服务器
        /// </summary>
        public async Task<bool> StartServer(string address = null, int port = 0)
        {
            if (_currentState != NetworkManagerState.Disconnected)
            {
                LogWarning("Cannot start server: already connected or connecting");
                return false;
            }

            SetState(NetworkManagerState.Connecting);

            try
            {
                string serverAddr = address ?? serverAddress;
                int serverPrt = port > 0 ? port : serverPort;

                var args = new NetworkRunnerStartArgs
                {
                    GameMode = NetworkRunner.GameMode.Server,
                    Address = new NetAddress(serverAddr),
                    MaxPlayers = maxPlayers
                };

                args.Address.Port = serverPrt;

                bool success = await _runner.StartGame(args);

                if (success)
                {
                    SetState(NetworkManagerState.Connected);
                    Log($"Server started successfully on {serverAddr}:{serverPrt}");
                    return true;
                }
                else
                {
                    SetState(NetworkManagerState.Error);
                    LogError("Failed to start server");
                    return false;
                }
            }
            catch (Exception ex)
            {
                SetState(NetworkManagerState.Error);
                LogError($"Server start error: {ex.Message}");
                OnConnectionError?.Invoke(ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 连接到服务器
        /// </summary>
        public async Task<bool> ConnectToServer(string address = null, int port = 0)
        {
            if (_currentState != NetworkManagerState.Disconnected)
            {
                LogWarning("Cannot connect: already connected or connecting");
                return false;
            }

            SetState(NetworkManagerState.Connecting);

            try
            {
                string serverAddr = address ?? serverAddress;
                int serverPrt = port > 0 ? port : serverPort;

                var args = new NetworkRunnerStartArgs
                {
                    GameMode = gameMode,
                    Address = new NetAddress(serverAddr),
                    MaxPlayers = maxPlayers
                };

                args.Address.Port = serverPrt;

                bool success = await _runner.StartGame(args);

                if (success)
                {
                    SetState(NetworkManagerState.Connected);
                    Log($"Connected to server at {serverAddr}:{serverPrt}");
                    return true;
                }
                else
                {
                    SetState(NetworkManagerState.Error);
                    LogError("Failed to connect to server");
                    return false;
                }
            }
            catch (Exception ex)
            {
                SetState(NetworkManagerState.Error);
                LogError($"Connection error: {ex.Message}");
                OnConnectionError?.Invoke(ex.Message);
                return false;
            }
        }

        /// <summary>
        /// 断开连接
        /// </summary>
        public async Task Disconnect()
        {
            if (_currentState == NetworkManagerState.Disconnected)
                return;

            SetState(NetworkManagerState.Disconnecting);

            try
            {
                if (_runner != null)
                {
                    await _runner.Shutdown();
                }

                SetState(NetworkManagerState.Disconnected);
                Log("Disconnected from network");
            }
            catch (Exception ex)
            {
                LogError($"Disconnect error: {ex.Message}");
                SetState(NetworkManagerState.Error);
            }
        }

        /// <summary>
        /// 获取网络统计信息
        /// </summary>
        public NetworkManagerStatistics GetStatistics()
        {
            return _statistics;
        }

        /// <summary>
        /// 重置统计信息
        /// </summary>
        public void ResetStatistics()
        {
            _statistics.Reset();

            if (enableRpcStatistics && RpcManager.Instance != null)
            {
                RpcManager.Instance.ResetStatistics();
            }

            if (_stateSynchronizer != null)
            {
                _stateSynchronizer.ResetStatistics();
            }
        }

        /// <summary>
        /// 获取当前网络状态
        /// </summary>
        public NetworkManagerState GetCurrentState()
        {
            return _currentState;
        }

        /// <summary>
        /// 检查是否已连接
        /// </summary>
        public bool IsConnected()
        {
            return _currentState == NetworkManagerState.Connected;
        }

        /// <summary>
        /// 检查是否为服务器
        /// </summary>
        public bool IsServer()
        {
            return _runner?.IsServer ?? false;
        }

        /// <summary>
        /// 检查是否为客户端
        /// </summary>
        public bool IsClient()
        {
            return _runner?.IsClient ?? false;
        }

        /// <summary>
        /// 获取本地玩家
        /// </summary>
        public PlayerRef GetLocalPlayer()
        {
            return _runner?.LocalPlayer ?? default;
        }

        /// <summary>
        /// 获取所有连接的玩家
        /// </summary>
        public List<PlayerRef> GetConnectedPlayers()
        {
            return _runner?.GetConnectedPlayers() ?? new List<PlayerRef>();
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化管理器
        /// </summary>
        private void InitializeManager()
        {
            try
            {
                // 创建错误处理器
                _errorHandler = new NetworkErrorHandler();

                // 创建网络运行器
                var runnerGO = new GameObject("NetworkRunner");
                runnerGO.transform.SetParent(transform);
                _runner = runnerGO.AddComponent<NetworkRunner>();

                // 设置回调
                _runner.OnPlayerJoined += HandlePlayerJoined;
                _runner.OnPlayerLeft += HandlePlayerLeft;
                _runner.OnConnectedToServer += HandleConnectedToServer;
                _runner.OnDisconnectedFromServer += HandleDisconnectedFromServer;

                _isInitialized = true;
                Log("Network manager initialized successfully");
            }
            catch (Exception ex)
            {
                LogError($"Failed to initialize network manager: {ex.Message}");
                _isInitialized = false;
            }
        }

        /// <summary>
        /// 设置组件
        /// </summary>
        private void SetupComponents()
        {
            try
            {
                // 设置状态同步器
                if (enableStateSynchronization)
                {
                    var syncGO = new GameObject("StateSynchronizer");
                    syncGO.transform.SetParent(transform);
                    _stateSynchronizer = syncGO.AddComponent<OptimizedStateSynchronizer>();
                }

                // 配置RPC管理器
                if (enableRpcOptimization && RpcManager.Instance != null)
                {
                    RpcManager.Instance.SetRunner(_runner);
                }

                Log("Network components setup completed");
            }
            catch (Exception ex)
            {
                LogError($"Failed to setup network components: {ex.Message}");
            }
        }

        /// <summary>
        /// 清理管理器
        /// </summary>
        private void CleanupManager()
        {
            try
            {
                if (_runner != null)
                {
                    _runner.OnPlayerJoined -= HandlePlayerJoined;
                    _runner.OnPlayerLeft -= HandlePlayerLeft;
                    _runner.OnConnectedToServer -= HandleConnectedToServer;
                    _runner.OnDisconnectedFromServer -= HandleDisconnectedFromServer;
                }

                Log("Network manager cleaned up");
            }
            catch (Exception ex)
            {
                LogError($"Cleanup error: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置状态
        /// </summary>
        private void SetState(NetworkManagerState newState)
        {
            if (_currentState != newState)
            {
                var oldState = _currentState;
                _currentState = newState;

                Log($"Network state changed: {oldState} -> {newState}");
                OnStateChanged?.Invoke(newState);
            }
        }

        /// <summary>
        /// 更新统计信息
        /// </summary>
        private void UpdateStatistics()
        {
            _statistics.Uptime = Time.time;
            _statistics.ConnectedPlayers = GetConnectedPlayers().Count;

            // 更新RPC统计
            if (enableRpcStatistics && RpcManager.Instance != null)
            {
                var rpcStats = RpcManager.Instance.GetStatistics();
                _statistics.TotalRpcsSent = rpcStats.TotalRpcsSent;
                _statistics.TotalRpcsReceived = rpcStats.TotalRpcsReceived;
            }

            // 更新同步统计
            if (_stateSynchronizer != null)
            {
                var syncStats = _stateSynchronizer.GetStatistics();
                _statistics.TotalSyncMessages = syncStats.TotalSyncMessages;
                _statistics.TotalBytesSent += syncStats.TotalBytesSent;
            }
        }

        /// <summary>
        /// 更新调试显示
        /// </summary>
        private void UpdateDebugDisplay()
        {
            if (Time.frameCount % 60 == 0) // 每秒更新一次
            {
                Log($"Network Stats: {_statistics}");
            }
        }

        #region 事件处理

        /// <summary>
        /// 处理玩家加入
        /// </summary>
        private void HandlePlayerJoined(PlayerRef player)
        {
            Log($"Player joined: {player}");
            OnPlayerJoined?.Invoke(player);
            _statistics.TotalPlayersJoined++;
        }

        /// <summary>
        /// 处理玩家离开
        /// </summary>
        private void HandlePlayerLeft(PlayerRef player)
        {
            Log($"Player left: {player}");
            OnPlayerLeft?.Invoke(player);
            _statistics.TotalPlayersLeft++;
        }

        /// <summary>
        /// 处理连接到服务器
        /// </summary>
        private void HandleConnectedToServer()
        {
            Log("Connected to server");
            SetState(NetworkManagerState.Connected);
        }

        /// <summary>
        /// 处理从服务器断开
        /// </summary>
        private void HandleDisconnectedFromServer()
        {
            Log("Disconnected from server");
            SetState(NetworkManagerState.Disconnected);
        }

        #endregion

        #region 日志方法

        private void Log(string message)
        {
            if (enableDebugLogs)
            {
                Debug.Log($"[UnifiedNetworkManager] {message}");
            }
        }

        private void LogWarning(string message)
        {
            if (enableDebugLogs)
            {
                Debug.LogWarning($"[UnifiedNetworkManager] {message}");
            }
        }

        private void LogError(string message)
        {
            Debug.LogError($"[UnifiedNetworkManager] {message}");
        }

        #endregion
    }

    #region 数据结构和枚举

    /// <summary>
    /// 网络管理器状态
    /// </summary>
    public enum NetworkManagerState
    {
        Disconnected,
        Connecting,
        Connected,
        Disconnecting,
        Error
    }

    /// <summary>
    /// 网络管理器统计信息
    /// </summary>
    [Serializable]
    public class NetworkManagerStatistics
    {
        [Header("连接统计")]
        public float Uptime;
        public int ConnectedPlayers;
        public long TotalPlayersJoined;
        public long TotalPlayersLeft;

        [Header("RPC统计")]
        public long TotalRpcsSent;
        public long TotalRpcsReceived;
        public long TotalRpcsFailed;

        [Header("同步统计")]
        public long TotalSyncMessages;
        public long TotalBytesSent;
        public long TotalBytesReceived;

        [Header("性能统计")]
        public float AverageLatency;
        public float PacketLossRate;
        public int DroppedConnections;

        public void Reset()
        {
            Uptime = 0;
            ConnectedPlayers = 0;
            TotalPlayersJoined = 0;
            TotalPlayersLeft = 0;
            TotalRpcsSent = 0;
            TotalRpcsReceived = 0;
            TotalRpcsFailed = 0;
            TotalSyncMessages = 0;
            TotalBytesSent = 0;
            TotalBytesReceived = 0;
            AverageLatency = 0;
            PacketLossRate = 0;
            DroppedConnections = 0;
        }

        public override string ToString()
        {
            return $"Network Stats - Uptime: {Uptime:F1}s, Players: {ConnectedPlayers}, " +
                   $"RPCs: {TotalRpcsSent}/{TotalRpcsReceived}, Sync: {TotalSyncMessages}, " +
                   $"Bytes: {TotalBytesSent + TotalBytesReceived}";
        }
    }

    #endregion
}
