using System;
using System.Collections.Generic;
using UnityEngine;

namespace CustomNetworking.Core.RPC
{
    /// <summary>
    /// RPC优先级队列 - 按优先级和时间戳排序消息
    /// </summary>
    public class PriorityQueue<T> where T : IPriorityQueueItem
    {
        private List<T> _items = new List<T>();
        
        public int Count => _items.Count;
        
        /// <summary>
        /// 入队
        /// </summary>
        public void Enqueue(T item)
        {
            _items.Add(item);
            HeapifyUp(_items.Count - 1);
        }
        
        /// <summary>
        /// 出队
        /// </summary>
        public T Dequeue()
        {
            if (_items.Count == 0)
                throw new InvalidOperationException("Queue is empty");
                
            T result = _items[0];
            _items[0] = _items[_items.Count - 1];
            _items.RemoveAt(_items.Count - 1);
            
            if (_items.Count > 0)
                HeapifyDown(0);
                
            return result;
        }
        
        /// <summary>
        /// 查看队首元素
        /// </summary>
        public T Peek()
        {
            if (_items.Count == 0)
                throw new InvalidOperationException("Queue is empty");
                
            return _items[0];
        }
        
        /// <summary>
        /// 清空队列
        /// </summary>
        public void Clear()
        {
            _items.Clear();
        }
        
        private void HeapifyUp(int index)
        {
            while (index > 0)
            {
                int parentIndex = (index - 1) / 2;
                if (_items[index].CompareTo(_items[parentIndex]) >= 0)
                    break;
                    
                Swap(index, parentIndex);
                index = parentIndex;
            }
        }
        
        private void HeapifyDown(int index)
        {
            while (true)
            {
                int leftChild = 2 * index + 1;
                int rightChild = 2 * index + 2;
                int smallest = index;
                
                if (leftChild < _items.Count && _items[leftChild].CompareTo(_items[smallest]) < 0)
                    smallest = leftChild;
                    
                if (rightChild < _items.Count && _items[rightChild].CompareTo(_items[smallest]) < 0)
                    smallest = rightChild;
                    
                if (smallest == index)
                    break;
                    
                Swap(index, smallest);
                index = smallest;
            }
        }
        
        private void Swap(int i, int j)
        {
            T temp = _items[i];
            _items[i] = _items[j];
            _items[j] = temp;
        }
    }
    
    /// <summary>
    /// 优先级队列项接口
    /// </summary>
    public interface IPriorityQueueItem : IComparable<IPriorityQueueItem>
    {
        RpcPriority Priority { get; }
        float Timestamp { get; }
    }
    
    /// <summary>
    /// RPC统计信息
    /// </summary>
    [Serializable]
    public class RpcStatistics
    {
        [Header("调用统计")]
        public long TotalRpcsSent;
        public long TotalRpcsReceived;
        public long TotalRpcsFailed;
        
        [Header("性能统计")]
        public float AverageProcessingTime;
        public float MaxProcessingTime;
        public long TotalProcessingTime;
        
        [Header("错误统计")]
        public long SerializationErrors;
        public long DeserializationErrors;
        public long MethodNotFoundErrors;
        public long ParameterMismatchErrors;
        
        [Header("带宽统计")]
        public long TotalBytesSent;
        public long TotalBytesReceived;
        public float AverageMessageSize;
        
        /// <summary>
        /// 重置统计信息
        /// </summary>
        public void Reset()
        {
            TotalRpcsSent = 0;
            TotalRpcsReceived = 0;
            TotalRpcsFailed = 0;
            AverageProcessingTime = 0;
            MaxProcessingTime = 0;
            TotalProcessingTime = 0;
            SerializationErrors = 0;
            DeserializationErrors = 0;
            MethodNotFoundErrors = 0;
            ParameterMismatchErrors = 0;
            TotalBytesSent = 0;
            TotalBytesReceived = 0;
            AverageMessageSize = 0;
        }
        
        /// <summary>
        /// 记录RPC发送
        /// </summary>
        public void RecordRpcSent(int messageSize)
        {
            TotalRpcsSent++;
            TotalBytesSent += messageSize;
            UpdateAverageMessageSize();
        }
        
        /// <summary>
        /// 记录RPC接收
        /// </summary>
        public void RecordRpcReceived(int messageSize, float processingTime)
        {
            TotalRpcsReceived++;
            TotalBytesReceived += messageSize;
            TotalProcessingTime += (long)(processingTime * 1000); // 转换为毫秒
            
            if (processingTime > MaxProcessingTime)
                MaxProcessingTime = processingTime;
                
            AverageProcessingTime = TotalProcessingTime / (float)TotalRpcsReceived / 1000f; // 转换回秒
            UpdateAverageMessageSize();
        }
        
        /// <summary>
        /// 记录RPC失败
        /// </summary>
        public void RecordRpcFailed(RpcErrorType errorType)
        {
            TotalRpcsFailed++;
            
            switch (errorType)
            {
                case RpcErrorType.SerializationError:
                    SerializationErrors++;
                    break;
                case RpcErrorType.DeserializationError:
                    DeserializationErrors++;
                    break;
                case RpcErrorType.MethodNotFound:
                    MethodNotFoundErrors++;
                    break;
                case RpcErrorType.ParameterMismatch:
                    ParameterMismatchErrors++;
                    break;
            }
        }
        
        private void UpdateAverageMessageSize()
        {
            long totalMessages = TotalRpcsSent + TotalRpcsReceived;
            if (totalMessages > 0)
            {
                AverageMessageSize = (TotalBytesSent + TotalBytesReceived) / (float)totalMessages;
            }
        }
        
        public override string ToString()
        {
            return $"RPC Stats - Sent: {TotalRpcsSent}, Received: {TotalRpcsReceived}, Failed: {TotalRpcsFailed}, " +
                   $"Avg Processing: {AverageProcessingTime:F3}s, Avg Size: {AverageMessageSize:F1} bytes";
        }
    }
    
    /// <summary>
    /// RPC错误类型
    /// </summary>
    public enum RpcErrorType
    {
        SerializationError,
        DeserializationError,
        MethodNotFound,
        ParameterMismatch,
        NetworkError,
        TimeoutError,
        AuthorizationError
    }
    
    /// <summary>
    /// RPC错误处理器接口
    /// </summary>
    public interface IRpcErrorHandler
    {
        void HandleError(RpcErrorType errorType, string message, Exception exception = null);
        bool ShouldRetry(RpcErrorType errorType, int retryCount);
        float GetRetryDelay(int retryCount);
    }
    
    /// <summary>
    /// 默认RPC错误处理器
    /// </summary>
    public class DefaultRpcErrorHandler : IRpcErrorHandler
    {
        private const int MaxRetries = 3;
        private const float BaseRetryDelay = 0.1f;
        
        public void HandleError(RpcErrorType errorType, string message, Exception exception = null)
        {
            string logMessage = $"[RPC Error] {errorType}: {message}";
            
            if (exception != null)
            {
                logMessage += $"\nException: {exception}";
            }
            
            switch (errorType)
            {
                case RpcErrorType.SerializationError:
                case RpcErrorType.DeserializationError:
                case RpcErrorType.ParameterMismatch:
                    Debug.LogError(logMessage);
                    break;
                    
                case RpcErrorType.MethodNotFound:
                    Debug.LogWarning(logMessage);
                    break;
                    
                case RpcErrorType.NetworkError:
                case RpcErrorType.TimeoutError:
                    Debug.LogWarning(logMessage);
                    break;
                    
                case RpcErrorType.AuthorizationError:
                    Debug.LogError(logMessage);
                    break;
                    
                default:
                    Debug.LogError(logMessage);
                    break;
            }
        }
        
        public bool ShouldRetry(RpcErrorType errorType, int retryCount)
        {
            if (retryCount >= MaxRetries)
                return false;
                
            switch (errorType)
            {
                case RpcErrorType.NetworkError:
                case RpcErrorType.TimeoutError:
                    return true;
                    
                case RpcErrorType.SerializationError:
                case RpcErrorType.DeserializationError:
                case RpcErrorType.MethodNotFound:
                case RpcErrorType.ParameterMismatch:
                case RpcErrorType.AuthorizationError:
                    return false;
                    
                default:
                    return false;
            }
        }
        
        public float GetRetryDelay(int retryCount)
        {
            // 指数退避策略
            return BaseRetryDelay * Mathf.Pow(2, retryCount);
        }
    }
}
